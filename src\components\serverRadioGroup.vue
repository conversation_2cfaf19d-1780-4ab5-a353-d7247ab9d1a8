<script lang="ts" setup>
import { Color } from '@/enums/colorEnum'

const props = defineProps<{
  modelValue: string | number
  items: Array<{
    priceTempId: string | number
    tempName: string
    price: string | number
    tempUnit: string
    label: string
  }>
}>()

const emit = defineEmits(['update:modelValue'])

const selectedValue = ref(props.modelValue)

watch(
  () => props.modelValue,
  (newValue) => {
    selectedValue.value = newValue
  },
)

function handleChange(evt: any) {
  for (let i = 0; i < props.items.length; i++) {
    if (props.items[i].priceTempId === Number(evt.detail.value)) {
      selectedValue.value = props.items[i].priceTempId
      break
    }
  }
  emit('update:modelValue', selectedValue.value)
}
</script>

<template>
  <radio-group class="o-vf-radio-group" @change="handleChange">
    <label v-for="(item, index) in items" :key="index" class="flex py-1">
      <radio
        style="transform: scale(0.7)"
        :color="Color.primary"
        :value="item.priceTempId.toString()"
        :checked="item.priceTempId === selectedValue"
      />
      <view class="f-serve flex justify-between">
        <view class="f-server-name flex-wrap pr-2 space-x-1">
          <text>{{ item.tempName }}</text>
          <up-tag
            v-if="item.label"
            :text="item.label"
            size="mini"
            style="transform-origin: left center; scale: 0.7"
            type="error"
          />
        </view>
        <view class="shrink-0">
          {{ item.price }}
          <text>{{ item.tempUnit }}</text>
        </view>
      </view>
    </label>
  </radio-group>
</template>

<style lang="scss" scoped>
.f-serve {
  $w: 560rpx;
  min-width: $w;
  max-width: $w;
}

.f-server-name {
  min-width: 0;
  word-wrap: break-word;
  overflow-wrap: break-word;
  white-space: normal !important;
}
</style>
